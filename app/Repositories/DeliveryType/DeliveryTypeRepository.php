<?php
namespace App\Repositories\DeliveryType;

use App\Enums\Status;
use App\Models\Backend\DeliveryType;
use App\Repositories\DeliveryType\DeliveryTypeInterface;
class DeliveryTypeRepository implements DeliveryTypeInterface{

    public function all(){
        //
    }

    public function get($id){
        //
    }

    public function store($request){
        //
    }

    public function update($request){
        //
    }

    public function delete($id){
      //
    }
}
