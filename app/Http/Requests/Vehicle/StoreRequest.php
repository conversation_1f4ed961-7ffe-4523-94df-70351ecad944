<?php

namespace App\Http\Requests\Vehicle;

use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'           => ['required'],
            'plate_no'       => ['required'],
            'chasis_number'  => ['required'],
            'model'          => ['required'],
            'year'           => ['required'],
            'brand'          => ['required'],
            'color'          => ['required'],
            'driver_id'      => ['required'],
            'description'    => ['required'],
            'status'         => ['required'],
        ];
    }
}
