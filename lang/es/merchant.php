<?php

return array (
  'dashboard'           => 'Panel',
  'merchant_dashboard'  => 'Tablero de Comerciantes',
  'title'               => 'Comerciante',
  'delivery_charge'     => 'Gastos de envío',
  'shop'                => 'Puntos de recogida',
  'company_information' => 'Información de la compañía',
  'opening_balance'     => 'Saldo de apertura',
  'vat'                 => 'IVA',
  'cod_charges'         => 'cargos de bacalao',
  'weight'              => 'Peso',
  'category'            => 'Categoría',
  'same_day'            => 'Mismo día',
  'next_day'            => 'Día siguiente',
  'sub_city'            => 'ciudad secundaria',
  'inside_city'         => 'Inside city',
  'outside_city'        => 'fuera de la ciudad',
  'create_delivery_charge'     => 'Crear cargo de entrega',
  'edit_delivery_charge'     => 'Editar cargo de envío',
  'create_merchant'     => 'Crear comerciante',
  'edit_merchant'       => 'Editar comerciante',
  'delivery_charge_added_msg' => 'El cargo de entrega del comerciante se agregó con éxito.',
  'added_msg'           => 'Comerciante añadido con éxito.',
  'update_msg'          => 'El comerciante se actualizó con éxito.',
  'delivery_charge_update_msg' => 'El cargo de entrega del comerciante se actualizó correctamente.',
  'delete_msg'          => 'Comerciante eliminada con éxito.',
  'delivery_charge_delete_msg'          => 'El cargo de entrega del comerciante se eliminó correctamente.',
  'error_msg'           => 'Algo salió mal.',
  'delivery_charge_error_msg'           => 'Algo salió mal.',
  'account_info'                        => 'Información de la cuenta',

  //merchant payment
  'payment_added_msg'           => 'Pago de comerciante agregado con éxito.',
  'payment_error_msg'           => 'Algo salió mal.',
  'payment_account_delete_msg'  => 'Cuenta de Merchant Payment eliminada con éxito.',
  'payment_update_msg'          => 'La cuenta de pago del comerciante se actualizó correctamente.',

  'payment_account'     => 'Cuenta de pago',
  'payment_info'       => 'Información del pago',
  'bank'               => 'Banco',
  'bank_name'               => 'Nombre del banco',
  'mobile'             => 'Móvil',
  'payment_method'     => 'Método de pago',
  'select_payment_method'     => 'Seleccionar método de pago',
  'select_bank'           => 'Seleccionar banco',

  'holder_name'           => 'Nombre de la titular',
  'branch_name'           => 'Nombre de la sucursal',
  'account_no'                  =>  'Número de cuenta',
  'routing_no'                  =>  'Número de enrutamiento',

  // banks
  "ab_bank_ltd"                 =>  "Banco AB Ltd.",
  "agrani_bank_ltd"             => "Banco Agrani Ltd.",
  "al_afarah_islami_bank_ltd"   =>  "Al-Arafah Islami Bank Ltd.",
  "bank_asia_ltd"               =>  "banco asia ltd",
  "brac_bank_ltd"               =>  "Banco Brac Ltd.",
  "city_bank_ltd"               => "banco de la ciudad ltd",
  "dbbl_agent_banking"          =>  "Agente bancario DBBL",
  "dhaka_bank_limited"          =>  "Banco de Dhaka Ltd.",
  "dutch_bangla_bank_ltd"       =>  "Dutch-Bangla Bank Ltd.",
  "eastern_bank_ltd"            =>  "Dutch-Bangla Bank Ltd.",
  "exim_bank_bangladesh_ltd"    => "Banco EximBangladesh Ltd.",
  "ific_bank_ltd"               =>  "Banco IFIC",
  "islami_bank_bangladesh_ltd"  =>  "Islami Bank Bangladesh Ltd.",
  "jamuna_bank_ltd"             =>  "Banco Jamuna Ltd.",
  "mercantile_bank_ltd"         =>  "Banco Mercantil Ltda.",
  "mutual_trust_bank_ltd"       =>  "banco de confianza mutua ltd.",
  "national_bank_ltd"           =>  "banco nacional ltd",
  "nrb_bank_ltd"                =>  "Banco NRB Ltd.",
  "nrb_commercial_bank_ltd"     =>  "Banco Comercial NRB Ltd.",
  "one_bank_ltd"                =>  "un banco ltd",
  "prime_bank_ltd"              =>  "primer banco ltd",
  "southeast_bank_ltd"          =>  "banco del sureste ltd.",
  "standard_bank_ltd"           =>  "banco estándar ltd",
  "standard_chartered_bank"     =>  "Banco Standard Chartered",
  "the_premiere_bank_ltd"       =>  "Premier Bank Ltd.",
  "trust_bank_ltd"              =>  "banco de confianza ltd.",
  "united_commercial_bank_ltd"  =>  "banco comercial unido ltd.",
  "uttara_bank_ltd"             =>  "Banco Uttara Ltd.",


  'select_mobile_company'      =>   'Seleccionar empresa',
  'mobile_company'             =>   'Empresa móvil',
  'bkash'                      =>   'Bkash',
  'nogod'                      =>   'Nagad',
  'rocket'                     =>   'Rocket',

  'mobile_no'                  =>  'No móviles.',
  'account_type'               =>  'Tipo de cuenta',

  'personal'                 =>   'Personal',
  'merchant'                 =>   'Comerciante',

  //merchant panel accounts
  'create_account'           =>   'Crear cuenta de pago',
  'edit_account'             =>   'Editar cuenta de pago',
  'account'                  =>    'Cuenta',
  'payment_accounts'         => 'Información del pago',
  'create'                   => 'Crear',
  'edit'                     => 'Editar',
  'business_name'            => 'Nombre del Negocio',
  'invoice'                  => 'Factura no',
  'track_id'                  => 'ID de rastreo',
  'amount'                   =>  'Cantidad',
  'payble_amount'            =>  'Cantidad a pagar',
  'payment_request'          =>  'Solicitud de pago',
  'invalid_otp'              =>  'OTP no válido',
  'cash'                     => 'Dinero'

);
