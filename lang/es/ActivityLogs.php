<?php
return [
    'merchant.business_name'=>'Comerciante',
    'payment_method'        => 'Método de pago',
    'bank_name'             =>  'Nombre del banco',
    'holder_name'           =>  'Nombre de la titular',
    'account_no'            =>  'número de cuenta',
    'branch_name'           =>  'Nombre de la sucursal',
    'routing_no'            =>  'Número de ruta',
    'mobile_company'        =>  'Empresa móvil',
    'mobile_no'             =>  'No móviles.',
    'account_type'          =>  'Tipo de cuenta',
    'user.name'             =>  'Usuaria',
    'month'                 =>  'Mes',
    'amount'                => 'Cantidad',
    'status'                =>  'Estado',
    'due'                   => 'Pendiente',
    'advance'               => 'Avance',
    'note'                  => 'Nota',
    'account_holder_name'   => 'Nombre de la titular',
    'gateway'               => 'Puerta',
    'name'                 =>  'Nombre',
    'assetcategorys.title' => 'Categoría de activos',
    'hubs.name'            => 'Centro',
    'supplyer_name'        => 'Proveedora',
    'quantity'             => 'Cantidad',
    'warranty'             => 'Garantía',
    'invoice_no'           => 'Factura no',
    'description'          => 'Descripción',
    'title'                => 'Título',
    'position'             => 'Posición',
    'category.name'        => 'Categoría',
    'weight'               => 'Peso',
    'same_day'             => 'Mismo día',
    'next_day'             => 'Día siguiente',
    'sub_city'             =>  'Ciudad secundaria',
    'outside_city'         =>  'fuera de la ciudad',
    'current_balance'      => 'Saldo actual',
    'deliveryman.user.name'=> 'Repartidor',
    'parcel.tracking_id'   => 'Parcela',
    'account.account_no'   => 'número de cuenta',
    'date'                 => 'Fecha',
    'receipt'              => 'recibo',
    'createdby.name'       => 'Creado por',
    'phone'                => 'Teléfono',
    'tracking_id'          => 'ID de rastreo',
    'details'              => 'Detalles',
    'fromAccount.account_no'=> 'De la cuenta',
    'toAccount.account_no'  => 'A la cuenta',
    'address'               =>  'DIRECCIÓN',
    'hub.name'              => 'Centro',
    'transaction_id'        => 'ID de transacción',
    'fromPayment.account_no'=> 'De la cuenta',
    'frompayment.account_no'=> 'De la cuenta',
    'business_name'         => 'Nombre del Negocio',
    'deliveryCharge.category.title'=> 'Categoría de entrega',
    'price'                  => 'Precio',
    'pickup_address'         => 'Dirección de entrega',
    'pickup_phone'           => 'Teléfono de recogida',
    'customer_name'          => 'Nombre del cliente',
    'customer_phone'         => 'Teléfono del cliente',
    'customer_address'       => 'Dirección del cliente',
    'invoice_no'             => 'Factura no',
    'cash_collection'        => 'Colección de dinero',
    'selling_price'          => 'Precio de venta',
    'delivery_charge'        => 'Gastos de envío',
    'total_delivery_amount'  => 'Cantidad total de entrega',
    'current_payable'        => 'Corriente por pagar',
    'merchantAccount.holder_name'=> 'Nombre de la titular',
    'permissions'            => 'permisos',
    'account'                =>  'Cuenta',
    'sms_send_status'        =>  'Estado de envío de SMS',
    'api_key'                =>  'Clave API',
    'secret_key'             =>  'Llave secreta',
    'department.title'       =>  'Departamento',
    'service'                =>  'Servicio',
    'priority'               =>  'Prioridad',
    'subject'                =>  'Sujeta',
    'original'               =>  'Original',
    'user.hub.name'          =>  'Centro',
    'slug'                   =>  'Babosa',
    'key'                    =>  'Llave',
    'value'                  =>  'Valor',
    'contact_no'             =>  'Número de contacto',
    'email'                  =>  'Correo electrónico',
    'password'               =>  'Contraseña'

];
