<?php

return array (
  'dashboard'           => 'Dashboard',
  'title'               => 'Parcels',
  'parcel'              => 'Parcel',
  'merchant'            => 'Merchant',
  'delivery_charge'     => 'Delivery Charge',
  'vat'                 => 'Vat',
    'priority'            => 'Priority',
    'normal'               => 'Normal',
    'high'                 => 'High',
    'map'                 => 'Map Show',
  'cod_charges'         => 'Cod charges',
  'weight'              => 'Weight',
  'weight_kg'           => 'Weight (KG)',
  'cod_amount'          => 'COD Amount',
  'category'            => 'Category',
  'note'                => 'Note',
  'same_day'            => 'Same day',
  'next_day'            => 'Next day',
  'sub_city'            => 'Sub city',
  'inside_city'         => 'Inside city',
  'outside_city'        => 'Outside city',
  'charge_details'      => 'Charge Details',
  'create_parcel'       => 'Create Parcel',
  'invoice'             => 'Invoice#',
  'price'               => 'Parcel Price',
  'recipient_info'       => 'Recipient Info',
  'parcel_type'         => 'Parcel Type',
  'delivery_type'       => 'Delivery Type',
  'edit_parcel'         => 'Edit Parcel',
  'duplicate'           => 'Duplicate Parcel',
  'added_msg'           => 'Parcel successfully added.',
  'update_msg'          => 'Parcel successfully update.',
  'delete_msg'          => 'Parcel successfully deleted.',
  'duplicate_msg'       => 'Parcel successfully duplicated.',
  'error_msg'           => 'Something went wrong.',
  'shop'                => 'Pickup Points',
  'pickup_phone'        => 'Pickup Phone',
  'pickup_address'      => 'Pickup Address',
  'cash_collection'     => 'Cash Collection',
  'selling_price'       => 'Selling Price',
  'customer_name'       => 'Customer Name',
  'customer_info'       => 'Customer Info',
  'customer_phone'      => 'Customer Phone',
  'customer_address'    => 'Customer Address',
  'liquid_check_label'  => 'Choose which needed for parcel',
  'liquid_fragile'      => 'Liquid/Fragile',
  'packaging'           => 'Packaging',
  'current_payable'     => 'Payable',
  'import_parcel'       => 'Import',
  'import'              => 'Import',
  'validation_log'      => 'Validation Log',
  'in_row_number'       => 'In Row Number',
  'sample'              => 'Download Sample',
  'status'              => 'Status',
  'status_update'       => 'Status Update',
  'browse_file'         => 'Browse File',
  'date'                => 'Date',
  'pickup_date'         => 'Pickup Time',
  'delivery_date'       => 'Delivery Time',
  'deliveryman'         => 'Delivery Man',
  'pickupman'           => 'Pickup Man',
  'parcel_bank'         => 'Parcels Bank',
  'parcel_import'       => 'Parcels import',
  'print'               =>  'Print',


  'parcel_create'                    => 'Parcel Create',
  'parcel_edit'                      => 'Parcel Edit',
  'parcel_logs'                      => 'Parcel Logs',
  'pickup_man_assign'                => 'Pickup man assign',
  'parcel_received_to_warehouse'     => 'Parcel Received to Warehouse',
  'delivery_man_assigned'            => 'Delivery man assigned',
  'parcel_details'                   => 'Parcel Details',
  'merchant_details'                 => 'Merchant Details',
  'customer_details'                 => 'Customer Details',
  'merchant_shop_details'            => 'Merchant Shop Details',
  'pickup_details'                   => 'Pickup Details',
  'parcel_info'                      => 'Parcel Info',
  'pickup_time'                      => 'Pickup Time',
  'delivert_time'                    => 'Delivery Time',


  'pickup_man'                       => 'Pickup man',
  'pickup_man_assigned'              => 'Pickup man successfully assigned',
  'pickup_scheduled'                 => 'Pickup re-schedule successfully assigned',
  'pickup_re_schedule'               => 'Pickup Re-Schedule',
  'received_by_pickup'               => 'Received By Pickup man',

  'received_by_pickup_success'        => 'Parcel successfully Received By Pickup man',
  'received_by_pickup_cancel_success' => 'Received By Pickup man canceled successfully',
  'received_warehouse_cancel'         => 'Received warehouse canceled successfully',
  'deliveryman_assign_cancel'         => 'Delivery man assign canceled successfully',
  'transfer_to_hub_canceled'          => 'Transfer to hub canceled successfully',
  'received_by_hub'                   => 'Transfer to hub received successfully',
  'received_by_hub_cancel'            => 'Received by hub canceled successfully',
  'return_assign_to_merchant_cancel_success'  => 'Return assign to merchant  canceled successfully',
  'return_received_by_merchant'      => 'Return received by merchant successfully',
  'return_received_by_merchant_cancel_success'  => 'Return received by merchant canceled successfully',
  'return_to_qourier_success'         =>  'Return to qourier successfully',
  'return_assign_to_merchant_success' =>  'Return Assign to merchant successfully',
  'return_assign_to_merchant_reschedule_success' =>  'Return Assign to merchant re-schedule successfully',
  'return_assign_to_merchant_reschedule_cancel_success' =>  'Return Assign to merchant re-schedule cancel successfully',

  'delivered_success'                => 'Parcel successfully delivered.',
  'delivered_cancel'                 => 'Parcel delivered canceled successfully',
  'partial_delivered_success'        => 'Partial delivered successfully',
  'partial_delivered_cancel'         => 'Partial delivered canceled successfully',

  'transfer_to_hub_success'          => 'Parcel successfully transferd to hub',
  'delivery_man_assign_success'      => 'Delivery man assigned successfully.',
  'delivery_reschedule_success'      => 'Delivery reschduled successfully.',
  'received_warehouse_success'       => 'Warehouse Recived successfully.',
  'required'                         => 'The fields is required!',
  'received_by_multiple_hub'         => 'Received by hub successfully',
  'feild_required'                   => 'Feild is required.',
  'edit_error_message'               => "You can't edit this!",
  'delete_error_message'             => "You can't delete this!",
  'total_cash_collection'            => 'Total Cash Collection',
  'delivery_man'                     => 'Delivery Man',
  'transfered_hub'                   => 'Transfered Hub',
  'hub'                              => 'Hub',
  'total_charge_amount'              => 'Charge Amount',
  'vat_amount'                       => 'Vat amount',

  'Cash_Collection'                 =>  'Cash collection',
  'Delivery_Charge'                 =>  'Delivery Charge',
  'Total_Charge'                    =>  'Total Charge',
  'Vat'                             =>  'Vat',
  'Net_Payable'                     =>  'Net Payable',
  'Current_payable'                 =>  'Current Payable',
  'Liquid/Fragile_Charge'           =>  'Liquid/Fragile Charge',
  'Cash_amount_including_delivery_charge'=> 'Cash amount including delivery charge',
  'Selling_price_of_parcel'         =>  'Selling price of parcel',
  'enter_invoice_number'            =>  'Enter invoice number',
  'settings_update_success'         => 'Settings Updated Successfully',
  'export_xlsx'                      => 'Excel',
  'export_csv'                      => 'CSV',
  'export_pdf'                      => 'PDF',
  'invoice_id'                      => 'Invoice Id',
  //new
  'tracking_id'                      => 'Tracking ID',
  'updated_on'                      => 'Updated On',
  'amount'                          => 'Amount',
  'payment'                          => 'Payment',
  'pending'                         =>'Pending',
  'in_progress'                     =>'In Progress',
  'warehouse'                       => 'Warehouse',
  'deliveryman_assigned'            => 'Deliveryman Assigned',
  'return_courier'                  => 'Return Courier',
  'delivered'                       => 'Delivered',

  'my_wallet'                       => 'My wallet',
  'wallet_history'                  => 'Wallet History',
  'wallet_request'       => 'Wallet Request',
  'are_you_approve_this_request' => 'Are You want to Approved This Request ?',
  'are_you_reject_this_request' => 'Are You want to Reject This Request ?',
  'wallet_request_approved_successfully' => 'Wallet request approved successfully',
  'wallet_request_rejected_successfully' => 'wallet request rejected successfully.',
  'search' => 'Search',
  'wallet' => 'Wallet',
  'wallet_balance' => "Wallet Balance",

  
  'add_to_wallet'        => 'Add to wallet',
  'quick_add'            => 'Quick Add',
  'quickly_add_money_from_given_options_and_recharge_your_wallet'  => 'Quickly add money from given options and recharge your wallet.',
  'wallet_recharge'        => 'Wallet Recharge',
  'wallet_recharge_successfully'          => 'Wallet recharge successfully',
  'wallet_recharge_update_successfully'   => 'Wallet recharge update successfully',
  'delete_wallet'          => 'Do you want to delete wallet ?',

  'wallet_addedd_successfully'     => 'Wallet Added successfully',
  'my_wallet'                      => 'My wallet',
  'wallet_history'                 => 'Wallet History',
  'wallet_request'                 => 'Wallet Request',
  'are_you_approve_this_request'   => 'Are You want to Approved This Request ?',
  'are_you_reject_this_request'    => 'Are You want to Reject This Request ?',
  'wallet_request_approved_successfully' => 'Wallet request approved successfully',
  'wallet_request_rejected_successfully' => 'wallet request rejected successfully.',
  'search'                         => 'Search',
  'wallet'                         => 'Wallet',
  'wallet_balance'                 => "Wallet Balance",
  'transaction_id'        => 'Transaction ID',
  'payment_method'       => 'Payment Method',
  'source' => 'Source',
  'wallet_request'       => 'Wallet Request',
  'paynow'               => 'Pay Now',
  'all_transaction'      => 'All Transaction',
  'recharge'             => 'Recharge', 
  'select'              => 'Select',
  'approve' => 'Approve',
  'reject' => 'Reject'
 

);
