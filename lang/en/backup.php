<?php

use App\Enums\AccountHeads;

return array (
  'title'                    => 'Database',
  'backup'                   => 'Backup',
  'download'                 => 'Download',
  'database_backup'          => 'Database Backup',
  'added_msg'                => 'Database Backup successfully added.',
  'update_msg'               => 'Database Backup successfully update.',
  'error_msg'                => 'Something went wrong.',
  'backup_description'       => 'Always back up the database to a different drive than the actual database. Then, if you get a disk crash, you will not lose your backup file along with the database.'

);
