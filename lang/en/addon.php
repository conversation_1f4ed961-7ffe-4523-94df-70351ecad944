<?php


return array (
  'title'                    => 'Addons',
  'name'                    => 'Name',
  'image'                    => 'Image',
  'version'                  => 'Version',
  'status'                  => 'Status',
  'purchase_code'            => 'Purchase Code',
  'zip_file'                => 'Zip File',
  'choose_file'                => 'Choose file',
  'install_update'           => 'Install/Update',
  'create_account'           => 'Create Addon',
  'edit_account'             => 'Edit Addon',
  'added_msg_'               => 'Addon successfully added.',
  'update_msg'               => 'Addon installed successfully',
  'update_msg_addon'         => 'This addon is updated successfully',
  'delete_msg'               => '<PERSON>don successfully deleted.',
  'error_msg_demo'           => 'This action is disabled in demo mode',
  'error_msg_install'        => 'his version is not capable of installing Addons, Please update.',
  'error_msg_install_extension' => 'Please enable ZipArchive extension.',

);
