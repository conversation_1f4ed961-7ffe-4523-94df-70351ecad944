<?php

return [

  'addons'               => 'Addons',
  'dashboard'            => 'Dashboard',
  'phone_book'           => 'Phone Book',
  'merchants'            => 'Merchants',
  'deliveryman'          => 'Delivery Man',
  'parcel'               => 'Parcels',
  'sms_send_settings'    => 'SMS Send Setting',
  'google_map_settings'  => 'GoogleMap Setting',
  'sms_settings'         => 'SMS Setting',
  'delivery_category'    => 'Delivery Category',
  'delivery_charge'      => 'Delivery Charge',
  'packaging'            => 'Packaging',
  'accounts'             => 'Accounts',
  'fund_transfer'        => 'Fund Transfer',
  'delivery_type'        => 'Delivery Type',
  'liquid_fragile'       => 'Liquid/Fragile',
  'expense'              => 'Expense',
  'income'               => 'Income',
  'user_role'            => 'Users & Roles',
  'parcel_bank'          => 'Parcels Bank',
  'account_heads'        => 'Account Heads',
  'account'              => 'Account',
  'bank_transaction'     => 'Bank Transactions',
  'salary'               => 'Salary',
  'general_settings'     => 'General Settings',
  'account_transaction'  => 'Account Transaction',
  'statements'           => 'Statements',
  'news_offer'           => 'Offers',
  'support'              => 'Ticket',
  'fraud_check'          => 'Deception Check',
  'hub_mange'            => 'Branch Manage',
  'hub_payment_request'  => 'Payment Request',
  'hub_payment'          => 'Branch Payments',
  'payments'             => 'Payments',
  'hubs'                 => 'Branch',
  'database_backup'      => 'Database Backup',
  'parcel_total_summery' => 'Total summery',
  'payroll'             => 'Payroll',
  'category_manage'     => 'Category Manage',
  'category_add'        => 'Category Add',
  'assets_category'     => 'Assets Category',
  'merchant_manage'     => 'Merchant Manage',
  'users'               => 'Users',
  'departments'         => 'Departments',
  'designations'        => 'Designations',
  'roles'               => 'Roles',
  'assets'              => 'Assets',
  'todo_list'           => 'Todo List',
  'active_logs'         => 'Active Logs',
  'menu'                => 'Menu',
  'logs'                => 'Logs',
  'select'              =>  'Select',
  'cancel'              =>  'Cancel',
  'submit'              =>  'Submit',
  'export'              =>  'Export',
  'date'                =>  'Date',
  'cod_charges'         =>   'COD Charges',
  'delivery_charges'    =>  'Delivery Charges',

  'profile'             => 'Profile',
  'settings'            =>  'Setting',
  'change_password'     =>  'Change password',
  'logout'              =>   'Logout',
  'regular'             =>   'Regular',
  'express'             =>    'Express',
  'pickup_request'      =>    'Pickup Request',
  'invoice'             =>  'Invoice',
  'social_login_settings' => 'Social login settings',
    //new
   'pay_out'                  => 'Online Payment Setup',
   'payment_gateway'          => 'Payment Gateway',
   'payment_details'           => 'Payment Details',
   'payout_setup'              => 'Online Payment Setup',
   'payout_setup_updated'       => 'Online Payment Setup updated successfully',
   'payout'                     => 'Payout',

   'payments_received'           => 'Payment Received',
   'online_payment_setup'        => 'Online Payment Setup',
   'payout_details'              => 'Payout Details',
   'invoice_generate_menually'   => 'Invoice Generate',
   'push_notification'           => 'Push Notification',
   'notification_settings'       => 'Notification Settings',
   'mail_settings' => 'Mail Settings',
   'payment_method' => 'Payment Method',
   'banks' => 'Banks',
   'mobile_banks' => 'Mobile Banks',
   'asset_management' => 'Asset Management',
   'driver'=> 'Driver',
];
