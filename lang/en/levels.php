<?php

return [
  'id'                     => '#',
  'add'                    => 'Add',
  'submit'                 => 'Submit',
  'cancel'                 => 'Cancel',
  'create'                 => 'Create',
  'update'                 => 'Update',
  'edit'                   => 'Edit',
  'duplicate'              => 'Duplicate',
  'show'                   => 'Show',
  'view'                   => 'View',
  'delete'                 => 'Delete',
  'actions'                => 'Actions',
  'active'                 => 'Active',
  'inactive'               => 'Inactive',
  'hub'                    => 'Hub',
  'name'                   => 'Name',
  'email'                  => 'Email',
  'username'               => 'Username',
  'phone'                  => 'Phone',
  'password'               => 'Password',
  'c_password'             => 'Confirm Password',
  'image'                  => 'Image',
  'address'                => 'Address',
  'status'                 => 'Status',
  'description'            => 'Description',
  'opening_balance'        => 'Opening Balance',
  'delivery_charge'        => 'Delivery Charge',
  'current_balance'        => 'Current Balance',
  'pickup_charge'          => 'Pickup Charge',
  'return_charge'          => 'Return Charge',
  'driving_license'        => 'Driving license',
  'unique_id'              => 'Unique ID',
  'business_name'          => 'Business Name',
  'vat'                    => 'Vat%',
  'trade_license'          => 'Trade License',
  'cod_charge'             => 'COD Charge',
  'nid'                    => 'NID',
  'list'                   => 'List',
  'dashboard'              => 'Dashboard',
  'slug'                   => 'Slug',
  'permission'             => 'Permission',
  'title'                  => 'Title',
  'details'                => 'Details',
  'joining_date'           => 'Joining Date',
  'designation'            => 'Designation',
  'department'             => 'Department',
  'user'                   => 'User',
  'assigned_date'          => 'Assigned Date',
  'position'               => 'Position',
  'category'               => 'Category',
  'weight'                 => 'Weight',
  'same_day'               => 'Same Day',
  'next_day'               => 'Next Day',
  'sub_city'               => 'Sub City',
  'outside_city'           => 'Outside City',
  'price'                  => 'Price',
  'gateway'                => 'Gateway',
  'balance'                => 'Balance',
  'account_holder_name'    => 'Account Holder Name',
  'account_no'             => 'Account No',
  'bank'                   => 'Bank',
  'branch_name'            => 'Branch Name',
  'mobile'                 => 'Mobile',
  'account_type'           => 'Account Type',
  'account_info'           => 'Account Info',
  'from_account'           => 'From Account',
  'to_account'             => 'To Account',
  'amount'                 => 'Amount',
  'date'                   => 'Date',
  'reject'                 => 'Reject',
  'cancel_reject'          => 'Cancel Reject',
  'process'                => 'Process',
  'cancel_process'         => 'Cancel Processed',
  'choose_file'            => 'Choose File',
  'roles'                  => 'Roles',
  'role'                   => 'Role',
  'logs'                   => 'Logs',
  'merchant_unique_id'     => 'Merchant Unique Id',
  'invoice_no'             => 'Invoice No',
  'delivery_category'      => 'Delivery Category',
  'delivery_type'          => 'Delivery Type',
  'packaging_title'        => 'Packaging Title',
  'packaging_price'        => 'Packaging Price',
  'cod'                    => 'COD',
  'selling_price'          => 'Selling Price',
  'liquid_fragile_amount'  => 'Liquid Fragile Amount',
  'packaging_amount'       => 'Packaging Amount',
  'cod_amount'             => 'COD Amount',
  'vat_amount'             => 'Vat Amount',
//   'total_delivery_amount'  => 'Total Delivery Amount',
  'total_delivery_amount'  => 'Total Charge Amount',
  'current_payable'        => 'Current Payable',
  'tracking_id'            => 'Ber Code',
  'note'                   => 'Note',
  'created_by'             => 'Created By',
  'hub_transfer'           => 'Hub Transfer',
  'to_hub'                 => 'To Hub',
  'from_hub'               => 'From Hub',
  'track_id'               => 'Tracking Id',
  'received_by_hub'        => 'Received By Hub',
  'filter'                 => 'Filter',
  'clear'                  => 'Clear',
  'from'                   => 'From',
  'parcel'                 => 'Parcel',
  'receipt'                => 'Receipt',
  'assign_pickup'          => 'Assign Pickup',
  'delivery_man_assign'    => 'Delivery Man Assign',
  'assign_return_merchant' => 'Assign return to merchant',
  'parcel_logs'            => 'Parcel Logs',
  'clone'                  => 'Clone',
  'print'                  => 'Print',
  'print_label'            => 'Print Label',
  'parcel_bank'            => 'Is it parcel bank ?',
  'select_bulk_type'       => 'Select Bulk',
  'account_heads'          => 'Account Heads',
  'account'                => 'Account',
  'type'                   => 'Type',
  'apiKey'                 => 'Api Key',
  'secretKey'              => 'Secret Key',
  'salary'                 => 'Salary',
  'customer_name'          => 'Customer Name',
  'month'                  => 'Month',
  'save_change'            => 'Save Change',
  'application_name'       => 'Application Name',
  'copyright'              => 'Copyright',
  'currency'               => 'Currency',
  'logo'                   => 'Logo',
  'favicon'                => 'Favicon',
  'pay_slip'               => 'Pay slip',
  'file'                   => 'File',
  'send'                   =>  'Send',
  'my_list'                =>  'My List',
  'search'                 =>  'Search',
  'hub_users'              =>  'Hub Users',
  'bank_name'              =>  'Bank Name',
  'holder_name'            =>  'Holder Name',
  'bangla'                 =>   'বাংলা',
  'english'                =>   'English',
  'created'                =>   'Created',
  'updated'                =>    'Updated',
  'number'                 =>   'Number',
  'old_password'         =>  'Old Password',
  'new_password'         =>   'New Password',
  'confirm_password'       =>  'Confirm Password',
  'pickup'                 =>  'Pickup',
  'customer_phone'         =>  'Customer phone',
  'estimetad_parcel'       => 'Estimetad Parcel',
  'total_vat'              => 'Total Vat Amount',
  'payment_period'         => 'Payment Period (days)',
  'map_key'                => 'GoogleMap Key',
  'fcm_secret_key'         => 'Firebase Secret Key',
  'fcm_topic'              => 'FCM Topic',
  'facebook'               => 'Facebook',
  'google'                 => 'Google',
  'app_id'                 => 'App ID',
  'app_secret'             => 'App Secret',
  'client_id'              => 'Client ID',
   'client_secret'          =>  'Client secret',
   'hindi'                  => 'हिन्दी',
   'arabic'                 => 'عربي',
   'franch'                 => 'Franch',
   'spanish'                => 'Spanish',
   'chinese'                => 'Chinese',

   'stripe_publishable_key' => 'Stripe Publishable Key',
   'stripe_secret_key'     => 'Stripe Secret Key',
   'amount_usd'            => 'Amount (USD)',
   'card_type'             => 'Card Type',
   'transaction_id'       => 'Transaction Id',

   'sslcommerz_store_id'   => 'SSl Commerz Store ID',
   'sslcommerz_store_password'=> 'SSl Commerz Store Password',

    //new
    'test_mode'                  => 'Sendbox Mode',
    'paypal_client_id'           => 'Client ID',
    'paypal_client_secret'       => 'Client Secret',
    'paypal_mode'                => 'Paypal Mode',

    'skrill_merchant_email'     => 'Skrill Merchant Email',
    'skrill_status'             => 'Skrill Status',
    'bkash_app_id'              => 'App ID',
    'bkash_app_secret'          => 'App Secret',
    'bkash_username'            => 'Username',
    'bkash_password'            => 'Password',
    'bkash_status'              => 'Status',
    'bkash_test_mode'           => 'Sendbox Mode',
    'sendbox_mode'               => 'Sendbox Mode',

    'aamarpay_store_id'          => 'Aamarpay store id',
    'aamarpay_signature_key'     => 'Aamarpay signature key',
    'save'                       => 'Save',

    //new
    'delivery_fee'                => 'Delivery fee',
    'total_cost'                  => 'Total cost',
    'amount_to_collect'           => 'Amount To Collection',
    'cash_collection'             => 'Cash Collection',
    'reference_name'         => 'Reference Name',
    'reference_phone'        => 'Reference Phone',
    'reference'              => 'Reference',
    'return_charges'         => 'Return Charges',
    'prefix'                 => 'Prefix',

    //new
    'aamarpay_payment_details' => 'Aamarpay Payment Details',
    'bkash_payment_details'    => 'bKash Payment Details',
    'paypal_payment_details'   => 'Paypal Payment Details',
    'skrill_payment_details'   => 'Skrill Payment Details',
    'sslcommerz_payment_details'=> 'SSLCommerz Payment Details',
    'razorpay_payment_details'  => 'Razorpay Payment Details',
    'stripe_payment_details'    => 'Stripe Payment Details',
    'author'                    => 'Author',
    'aamarpay_payout_details' => 'Aamarpay Payout Details',
    'bkash_payout_details'    => 'bKash Payout Details',
    'paypal_payout_details'   => 'Paypal Payout Details',
    'skrill_payout_details'   => 'Skrill Payout Details',
    'sslcommerz_payout_details'=> 'SSLCommerz Payout Details',
    'stripe_payout_details'    => 'Stripe Payout Details',
    'cash_on_delivery'         => 'Cash On Delivery',
    'delivery_info'            => 'Delivery Info',
    'sender_info'              => 'Sender Info',
    'recipient_info'           => 'Recipient Info',
    'paypal'                    => 'Paypal',
    'razorpay'                  => 'Razorpay',
    'stripe'                    => 'Stripe',
    'skrill'                    => 'Skrill',
    'aamarpay'                  => 'Aamarpay',
    'sslcommerz'                => 'SSLCommerz',
    'bkash'                     => 'bKash',
    'razorpay_key'              => 'Razorpay key',
    'razorpay_secret'           => 'Razorpay Secret',
    'paystack_key'              => 'Paystack Key',
    'paystack_secret'           => 'Paystack Secret',
    'import_title'              => 'Please check this before importing your file',
    'import_title_2'            => 'Uploaded File type must be xlsx',
    'import_title_3'            => 'The file must contain pickup_address, pickup_phone,
    cash_collection, selling_price, customer_name, invoice_no, customer_phone, customer_address.',
    'import_title_4'            => 'must be numeric',
    'pay_now'                   => 'Pay Now',
    'twilio_sid'                => 'Twilio SID',
    'twilio_token'              => 'Twilio Token',
    'nexmo_key'                 => 'NEXMO Key',
    'nexmo_secret_key'          => 'NEXMO Secret Key',

    'pending'                   => 'Pending',
    'processing'                => 'Processing',
    'resolved'                  => 'Resolved',
    'closed'                    => 'Closed',


    //new update 11-6-23
    'primary_color'   => 'Primary Color',
    'text_color'      => 'Text Color',
    'front_web'       => 'Front Web',
    'social_link'     => 'Social Link',
    'icon'            => 'Icon',
    'link'            => 'Link',
    'social'          => 'Social',
    'enter_link'      => 'Enter Link',
    'enter_icon' => 'Enter Icon',
    'click_here'      => 'Click here',
    'social_link_added'    => 'Social link added successfully.',
    'social_link_updated'  => 'Social link updated successfully.',
    'social_link_deleted'  => 'Social link deleted successfully.',
    'service'              => 'Service',
    'service_added'        => 'Service added successfully.',
    'service_updated'      => 'Service updated successfully.',
    'service_deleted'      => 'Service deleted successfully.',

    'why_courier'              => 'Why Courier',
    'why_courier_added'        => 'Why courier added successfully.',
    'why_courier_updated'      => 'Why courier updated successfully.',
    'why_courier_deleted'      => 'Why courier deleted successfully.',
    'faq'                      => 'FAQ',
    'faq_added'                => 'FAQ added successfully.',
    'faq_updated'              => 'FAQ updated successfully.',
    'faq_deleted'              => 'FAQ deleted successfully.',
    'question'                 => 'Question',
    'answer'                   => 'Answer',
    'Enter_question'           => 'Enter question',
    'partner'                  => 'Partner',
    'partner_added'            => 'Partner added successfully.',
    'partner_updated'          => 'Partner updated successfully.',
    'partner_deleted'          => 'Partner deleted successfully.',
    'blogs'                    => 'Blogs',
    'blog'                     => 'Blog',
    'blog_added'               => 'Blog added successfully.',
    'blog_updated'             => 'Blog updated successfully.',
    'blog_deleted'             => 'Blog deleted successfully.',

    'pages'                    => 'Pages',
    'page_updated'             => 'Page updated successfully.',
    'section'                  => 'Section',
    'sections'                  => 'Sections',
    'section_updated'          => 'Section updated successfully.',
    'banner'                   => 'Banner',
    'happy_achievement'        => 'Happy Achievement',
    'about_us'                 => 'About Us',
    'subscribe'                => 'Subscribe',
    'app_download_link'        => 'App Download',

    'first_title'              => 'First Title',
    'middle_title'             => 'Middle Title',
    'last_title'               => 'Last Title',
    'Enter_first_title'        => 'Enter first title',
    'Enter_middle_title'       => 'Enter middle title',
    'Enter_last_title'         => 'Enter last title',
    "branches"                 => "Branches",
    'count' => 'Count',
    'parcel_delivered'        => 'Parcel Delivered',
    'happy_merchant'          => 'Happy Merchant',
    'positive_reviews'        => 'Positive reviews',
    'Enter_about_us'          => 'Enter About Us',
    'Enter_icon'              => 'Enter Icon',
    'Enter_count'             => 'Enter Count',
    'Enter_title'             => 'Enter title',
    "Enter_about_us"          =>  'Enter About Us',
    'play_store'              => 'Play Store',
    'ios_store'                => 'iOS Store',
    'home'                    => 'Home',
    'pricing'                 => 'Pricing',
    'tracking'                => 'Tracking',
    'about'                   => 'About',
    'contact'                 => 'Contact',
    'login'                   => 'Login',
    'register'                => 'Register',
    'track_now'               => 'Track Now',
    'up_to'                   => 'Up To',
    'why'                     => 'Why',
    'our_services'            => 'Our Services',
    'our_partner'             => 'Our Partners',
    'happy_achievement'       => 'Happy Achievement',
    'available_services'      => 'Available Services',
    'terms_of_use'            => 'Terms of Use',
    'privacy_policy'          => 'Privacy And Policy',
    'download_app'            => 'Download App',
    'parcel_tracking_no'      => 'Parcel Tracking No',
    'enter_tracking_id'       => 'Enter tracking id',
    'parcel_tracking'         => 'Parcel Tracking',
    'read_our_commonly_asked_questions'=>'Read our commonly asked questions',
    'successfully_subscribed' =>"Successfully subscribed.",
    'already_subscribed'      =>'Already Subscribed',
    'logout'                  => 'Logout',
    'message_sended_successfully'=>'Message Sent successfully.',
    'message'                 => 'Message',
    'enter_your_message'      =>'Enter your message',
    'enter_email'             =>'Enter email',
    'enter_name'              => 'Enter name',
    'enter_subject'           => 'Enter subject',
    'subject'                 => 'Subject',
    'contact_us'              =>'Contact us',
    'light_logo'              => 'Light Logo',
    'blogs'                   => 'Blogs',
    'blog_details'            =>'Blog Details',
    'latest_blogs'            =>'Latest Blogs',
    'map_link'                =>'Map Link',
    'enter_map_link'          =>'Enter Map Link',
    'latest_services'         =>'Latest Services',
    'wallet_use_activation'   =>'Wallet Use Activation',
    'twilio_from'        => 'Twilio from',
    'mail_mailer'        => 'Mail Mailer',
    'mail_host'          => 'Mail Host',
    'mail_port'          => 'Mail port',
    'mail_username'      => 'Mail Username',
    'mail_password'      => 'Mail Password',
    'mail_encryption'    => 'Mail Encryption',
    'mail_from_address'  => 'Mail From Address',
    'mail_from_name'     => 'Mail From Name',
    'select_company'     => 'Select Company',
'delivery_method' => 'Delivery method',
    'delivery_method_name'=> 'Delivery Method Name',
    'enter_delivery_method_name'=> 'Enter Delivery Method',
    'barcode'=>'Barcode',
    'barcode_print'=>'Barcode Print',
    'pickup_point'  => 'Pickup Point',
    'shop_number'  => 'Shop Number',
    'P_Charge'=>'P.Charge',
    'total_delivery_cost'=>'Total delivery cost',
    'Delivery_Charge'=>'Delivery charge',
    'liquide_fragile'=>'Liquide/Fragile',
    'parcel_info'    =>'Parcel Info',
    'plate_no'       => 'Plate No',
    'chasis_number'  => 'Chasis Number',
    'model'          => 'Model',
    'year'           => 'Year',
    'brand'          => 'Brand',
    'color'          => 'Color',
    'fuels'          => 'Fuels',
    'fuel'  => 'Fuel',
    'fuel_type'  => 'Fuel Type',
    'invoice_of_fuel'  =>  'Invoice of fuel',
    'maintenance' => 'Maintenance',
    'maintenances' => 'Maintenances',

    'repair_details'                  => 'Repair details',
    'spare_parts_purchased_details'   => 'Spare parts purchased details',
    'invoice_of_the_purchases'        => 'Invoice of the purchases',
    'accidents' => 'Accidents',
    'accident'=>'Accident',

    'date_of_accident'       =>  'Date of accident',
    'driver_responsible'     =>  'Driver responsible',
    'cost_of_repair'         =>  'Cost of repair',
    'spare_parts'            =>  'Spare parts',
    'upload_documents'        =>  'Upload documents',
    'insurance_registration' => 'Insurance registration',
    'reminder_to_renew_insurance' => 'Reminder to renew insurance',
    'assets'=> 'Assets',
    'kpi' => 'KPI',
    'on_time_delivery_rate' => 'On-Time Delivery Rate',
    'delivery_time' => 'Delivery Time',
    'average_deliveries_time' => 'Average Deliveries Time',
    'Delivery_Cost_per_Package' => 'Delivery Cost Per Package',
    'single_parcel_cost' => 'Single parcel cost',
    'add_other_cost'=>'Add other cost',
    'fuel_cost' => 'Fuel Cost',
    'labor_cost' => 'Labor Cost',
    'other_associated_cost' => 'Other associated Cost',
    'distance_km'=>'Distance (KM)',
    'Cost_per_Distance'=>'Average Cost per Distance',
    'single_parcel_cost_per_km'=>'Single Parcel cost (Per KM)',
    'total_km'   => 'Total KM',
    'single_parcel_average_km'   => 'Single Parcel Average KM',
    'Customer_Satisfaction_Score' => 'Customer Satisfaction (CSAT) Score',
    'delivery_priority' => 'Delivery Priority',
    'charge' =>'Charge',
    'action'=> 'Action',
    'delivery_priority_charge'=>'D.Priority Charge',
    'start_date'=>'Start Date',
    'end_date'=>'End Date',
    'due_days'=>'Due Days',
    'assign'=>'Assign',
    'driver'=>'Driver',
    'assign_to_driver'=>'Assign To Driver',
    'from_date' =>'From Date',
    'to_date'=>'To Date',
    'reports'=>'Reports',
    'asset_management' =>'Asset Management',
    'assigned_drivers'=>'Assigned Drivers',
    'total_days'=>'Total Days',
    'reminder_to_renew_registration'=>'Reminder to renew registration',
    'back'=>'Back',
    'paystack_payout_details' => 'Paystack Payout Details',
    'paystack_payment_details' => 'Paystack Payment Details',
    'paystack_public_key' => 'Paystack Public Key',
    'paystack_secret_key' => 'Paystack Secret Key',
    'paystack_callback_url' => 'Paystack Callback URL',

    'paymob_api_key'     => 'API key',
    'paymob_secret_key'  => 'Secret key',
    'paymob_mode'        => 'Paymob mode',

    'paymob_iframe_id'       => 'Iframe ID',
    'paymob_integration_id'  => 'Integration ID',
    'paymob_hmac'            => 'HMAC',

    'click_send_api_key' => 'Click Send API Key',
    'click_send_username' => 'Click Send Username',
    'click_send_url' => 'Click Send URL',
];
