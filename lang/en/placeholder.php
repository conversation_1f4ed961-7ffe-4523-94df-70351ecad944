<?php
return [
    'Enter_title'           =>   'Enter Title',
    'Enter_description'     =>   'Enter Description',

    'Enter_phone'           =>    'Enter Phone',
    'Enter_mobile'           =>   'Enter Mobile',
    'enter_email'           =>    'Enter Email',
    'Enter_name'            =>    'Enter Name',
    'Enter_contact_no'      =>    'Enter Contact No',
    'Enter_password'        =>    'Enter Password',
    'Enter_tracking_id'     =>    'Enter Tracking Id',
    'Enter_nid_number'      =>    'Enter NID Number',
    'Enter_return_charge'   =>    'Enter Return charge',
    'Hub_name'              =>    'Hub Name',
    'phone'                 =>    'Phone',
    'Enter_delivery_charge' =>    'Enter Delivery Charge',
    'Enter_pickup_charge'   =>    'Enter Pickup Charge',
    'Enter_name'            =>    'Enter Name',
    'Enter_mobile_number'   =>    'Enter Mobile Number',
    'Enter_weight'          =>     'Enter Weight',
    'Enter_phone'           =>    'Enter Phone',
    'Enter_address'         =>    'Enter Address',
    'Enter_price'           =>     'Enter Price',
    'Enter_Amount'          =>    'Enter Amount',
    'Enter_Transaction_ID'  =>    'Enter transaction ID',
    'low'                   =>    'Low',
    'Medium'                =>    'Medium',
    'High'                  =>    'High',
    'Enter_Subject'         =>    'Enter Subject',
    'Enter_message'         =>    'Enter Message',
    'download_file'         =>    'Download File',
    'Enter_Supplyer_Name'   =>    'Enter Supplyer Name',
    'Enter_copyright'       =>     'Enter Copyright',
    'Enter_Quantity'        =>    'Enter Quantity',
    'Enter_Warranty'        =>    'Enter Warranty',
    'Enter_Invoice_No'      =>    'Enter Invoice No',
    'Enter_Position'        =>     'Enter Position',
    //account heads
    'Payment received from Merchant'    => 'Payment received from Merchant',
    'Cash received from delivery man'   => 'Cash received from delivery man',
    'Others'                            => 'Others',
    'Payment receive from hub'          => 'Payment receive from hub',
    //end account heads
    'holder_name'                       => 'Holder Name',
    'account_no'                        =>  'Account No',
    'Enter_Routing_Number'              =>  'Enter Routing Number',
    'Bank_name'                         => 'Bank Name',
    'Opening_Balance'                   => 'Opening Balance',
    'Account_Holder_Name'               =>  'Account Holder Name',
    'Enter_account_no'                  => 'Enter Account No',
    'Bank_name'                         =>  'Bank Name',
    'Enter_branch_name'                 =>  'Enter Branch Name',
    'Enter_opening_balance'             => 'Enter Opening Balance',
    'persional'                         => 'Persional',
    'current_balance'                   => 'Current Balance',
    'account'                           => 'Account',
    'enter_business_name'               => 'Enter Business Name',
    'Enter_vat'                         => 'Enter VAT',
    'enter_same_day'                    => 'Enter Same Day',
    'enter_next_day'                    =>  'Enter Next Day',
    'enter_sub_city'                    =>  'Enter Sub City',
    'enter_outside_city'                =>  'Enter Outside City',
    'Liquid_Fragile'                    =>  'Liquid/Fragile',
    'Enter_gateway_name'                =>  'Enter Gateway Name',
    'Enter_api_key'                     =>  'Enter API Key',
    'Enter_secret_key'                  => 'Enter Secret Key',
    'Enter_username'                    =>  'Enter Username',
    'Enter_user_password'               => 'Enter User Password',
    'Enter_api_url'                     => 'Enter API URL',
    'enter_old_password'                => 'Enter Old Password',
    'enter_new_password'                => 'Enter New Password',
    'enter_confirm_password'            =>  'Enter Confirm Password',
    'app_id'                            => 'Enter App ID',
    'app_secret'                        => 'Enter App secret',
    'client_id'                         => 'Enter client ID',
    'client_secret'                     => 'Enter client secret',
    'enter_name'                        => 'Enter Name',
    'enter_asset_type'                   => 'Enter Asset Type',
    'enter_plate_no'                   => 'Enter Plate No',
    'enter_model'                       => 'Enter Model',
    'enter_chasis_number'                   => 'Enter Chasis Number',
    'enter_year'                   => 'Enter Year',
    'enter_brand'                   => 'Enter Brand',
    'enter_color'                   => 'Enter Color',
    'enter_fuel_type'                   => 'Enter Fuel Type',
    'enter_amount'                   => 'Enter Amount',
    'enter_cost_of_repair'                   => 'Enter Cost of Repair',
    'enter_spare_parts'                   => 'Enter Spare Parts',
];
