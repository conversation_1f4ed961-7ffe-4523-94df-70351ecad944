<?php
use App\Enums\TodoStatus;
return [

  'dashboard'            => 'Dashboard',
  'to_do'                => 'To Do',
  'to_do_add'            => 'To Do Add',
  'to_do_edit'           => 'To Do Update',
  'to_do_delete'         => 'To Do Delete',
  'to_do_list'           => 'To Do List',
  'title'                => 'Title',
  'description'          => 'Description',
  'assign'               => 'Assign',
  'date'                 => 'Date',
  'status'               => 'Status',
  'action'               => 'Action',
  'delete'               => 'Delete',
   'sl'                  => 'SL',
  'status_update'        => 'Status Update',
  'added_msg'            => 'Todo Insert successfully!',
  'error_msg'            => 'Opss! Something is rong',
  'note'                 => 'Note',
    TodoStatus:: PENDING=>'PENDING',
    TodoStatus:: PROCESSING=>'PROCESSING',
    TodoStatus:: COMPLETED=>'COMPLETED',
    'todo_processing_success'              => 'To Do Processing Success',
    'todo_compete_success'                 => 'To Do Complete Success',
    'update_msg'                           => 'Todo Update successfully!',


];
