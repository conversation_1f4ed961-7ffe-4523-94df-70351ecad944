<?php
return [
    'title'                 => 'Reports',
    'dashboard'             => 'Dashboard',
    'parcel_reports'        => 'Parcel Status Reports',
    'parcel_wise_reports'   => 'Parcel Wise Reports',
    'total_charge_amount'   =>  'Total Charge Amount',
    'total_vat_amount'      =>  'Total Vat Amount',
    'total_current_payable' =>  'Total Current Payable',
    'total_cash_collection' =>  'Total Cash Collection',
    'print'                 =>  'Print',
    'parcel_wise_profit'    =>  'Parcel Wise Profit',
    'parcel_tracking_id'    =>  'Parcel Tracking Id',
    'details'               =>  'Details',
    'profit'                => 'Profit',
    'total'                 => 'Total',
    'parcel_total_summery'  => 'Total Summery',
    'salary_reports'        => 'Salary Reports',
    'salary'                => 'Salary',
    'paid_amount'           => 'Paid Amount',
    'user_details'          => 'User Details',
    'month'                 => 'Month',
    'merchant_hub_deliveryman' => 'Merchant/Branch/deliveryman',
    'merchant'              =>  'Merchant',
    'hub'                   =>  'Branch',
    'delivery_man'          =>  'Delivery Man',
    'user_types'             =>  'User Type',
    'merchant_reports'      => 'Merchant Reports',
    'hub_reports'           => 'Branch Reports',
    'delivery_man_reports'  => 'Delivery man Reports',
    'type'                  => 'Type',
    'amount'                => 'Amount',
    'merchant_details'      =>  'Merchant Details',
    'deliveryman_details'   =>  'Delivery man Details',
    'hub_details'           => 'Branch Details',
    'titles'                => 'Title',
    'amount'                =>  'Amount',
    'type'                  =>  'Type',
    'deliveryman'           =>  'Delivery man',
    'income'                =>  'Income',
    'expense'               =>  'Expense',

    'total_statistics'      =>  'Total Statistics',
    'payable_to_merchant'   =>  'Payable To Merchant',
    'payable'               =>  'Payable',
    'parcel_status'         =>  'Parcel Status',
    'count'                 =>  'Count',

    'total_payable_to_merchant'=> 'Total payable to merchant',
    'total_paid_to_merchant'=>  'Total paid to merchant',
    'total_delivery_charge' => 'Total delivery charge',
    'total_vat'             => 'Total Vat',
    'merchant_balance'      => 'Merchant Balance',

    'current_balance'       => 'Current Balance',
    'balance'               => 'Balance',
    'hub_payable'           => 'Payable',
    'profit_info'            => 'Profit Info',
    'payable_to_merchant'   =>  'Payable to merchant',


    //more

    'Total_Delivery_Charge' => 'Total Delivery Charge',
    'COD_Charge'            => 'COD Charge',
    'Total_Vat'             => 'Total Vat',
    'F./L.Charge'           => 'F./L.Charge',
    'P.Charge'              => 'P.Charge',
    'Total_Profit'          => 'Total Profit',

    'Total_payable_merchant(COD)'           =>  'Total payable merchant (COD)',
    'Total_paid_to_merchant(with Pending)'  =>  'Total paid to merchant (with Pending)',
    'Total_paid_by_Merchant'                =>   'Total paid by merchant',
    'Total_Delivery_Charge(Including VAT)'  =>   'Total deliveryman charge (Including VAT)',
    'Pending_Payments'                      =>   'Pending payments',

    'Hub_info'                              =>  'Branch Info',
    'total_cash_collection'                 =>  'Total Cash Collection',
    'Cash_received_from_delivery_man'       =>  'Cash received from delivery man',
    'Total_paid_to_Hub'                     =>  'Total paid to branch',
    'Bank_Cash_Info'                        =>  'Bank Cash Info',
    'Delivery_man_info'                     =>   'Delivery man info',
    'Total_Income'                          =>   'Total Income',
    'Total_paid_to_hub'                     =>   'Total paid to Branch',
    'Total_Payable_to_hub'                  =>   'Total Payable to Branch',
    'Cash_Collection_Info'                  =>   'Cash Collection Info',


    'Total_cash_on_Delivery'               =>   'Total cash on delivery',
    'Total_paid_by_delivery_man'           =>   'Total paid by delivery man',
    'Total_paid_to_delivery_man'           =>   'Total paid to delivery man',
    'Total_paid_by_Hub'                    =>   'Total paid by Branch',
    'Payable_to_Merchant'                  =>    'Payable to merchant',

    'Total_bank_opening_balance'           =>    'Total bank opening balance',
    'Current_Cash_Balance'                 =>    'Current Cash Balance',
 
    'parcel_info'                          => 'Parcel Information',
    'total_created_parcel'                 => 'Total created parcels',
    'total_delivered'                      => 'Total delivered',
    'total_partial_delivered'               => 'Total partial delivered',
    'total_delivered_cash_collection'      => 'Total delivered cash collection (with partial delivered)',
    'total_created_cash_collection'        => 'Total created cash collection',
    'total_in_transit'                     => 'Total in-Transit',
    'total_returned_to_merchant'           => 'Total Returned to Merchant',
    'Profit'                               => 'Profit',
    'delivered_partial_delivered'          => 'Delivered and Partial Delivered',
    'total_cod_amount'                     => 'Total COD Amount',
    'total_delivery_charge_amount'         => 'Total Delivery Charge Amount',
    'total_vat_amount'                     => 'Total Vat Amount',
    'total_profit_amount'                  => 'Total profit_amount',
    'total_account_balance'                => 'Total Account Balance',
    'total_account_opening_balance'        => 'Total Account Opening Balance',
    'total_fund_transfer_amount'            => 'Total Fund Transfer Amount',

    'total_payable_merchant'                => 'Total Payable to Merchant',
    'total_due_amount'                      => 'Total Due Amount (without Pending payments)',
    'return_charges'                        => 'Return charges',
    'total_charges'                         => 'Total charges',
    'delivery_man_income'                   => 'Delivery man income'


];
