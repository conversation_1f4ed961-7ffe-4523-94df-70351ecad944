<?php

return [
            'dashboard'           =>'Dashboard',
            'modules'           =>'Modules',
            'permissions'       =>'Permissions',

            //modules

            'logs'                  =>  'Logs',
            'hubs'                  =>  'Branch',
            'accounts'              =>  'Accounts',
            'income'                =>  'Income',
            'expense'               =>  'Expense',
            'todo'                  =>  'Todo',
            'fund_transfer'         =>  'Fund Transfer',
            'roles'                 =>  'Roles',
            'designations'          =>  'Designations',
            'departments'           =>  'Departments',
            'users'                 =>  'Users',
            'merchant'              =>  'Merchant',
            'payments'              =>  'Payments',
            'parcel'                =>  'Parcel',
            'delivery_man'          =>  'Delivery Man',
            'delivery_category'     =>  'Delivery Category',
            'delivery_charge'       =>  'Delivery Charge',
            'delivery_type'         =>  'Delivery Type' ,
            'liquid_fragile'        =>  'Liquid/Fragile',
            'packaging'             =>  'Packaging',
            'category'              =>  'Category',
            'account_heads'         =>  'Account Heads',
            'salary'                =>  'Salary',
            'support'               =>  'Ticket',
            'sms_settings'          =>  'SMS Settings',
            'sms_send_settings'     =>  'SMS send settings',
            'general_settings'      =>  'General Settings',
            'notification_settings' =>  'Notification Settings',
            'push_notification'     =>  'Push Notification',
            'asset_category'        =>   'Asset Category',
            'news_offer'            =>   'Offers',
            'cash_received_from_delivery_man'=>'Cash received from delivery man',

            'reports'               => 'Reports',
            'salary_generate'       => 'Salary Generate',
            'reply'                 => 'Reply',
            'fraud'                 =>  'Deception',

            //permissions
            'read'                  =>  'Read',
            'create'                =>  'Create',
            'update'                =>  'Update',
            'delete'                =>  'Delete',
            'incharge_read'         =>  'Incharge Read' ,
            'incharge_create'       =>  'Incharge Create',
            'incharge_update'       =>  'Incharge Update' ,
            'incharge_delete'       =>  'Incharge Delete',
            'incharge_assigned'     =>  'Incharge Assigned',
            'delivery_charge_read'  =>  'Delivery Charge Read' ,
            'delivery_charge_create'=>  'Delivery Charge Create',
            'delivery_charge_update'=>  'Delivery Charge Update',
            'delivery_charge_delete'=>  'Delivery Charge Delete',
            'shop_read'             =>  'Pickup Point Read',
            'shop_create'           =>  'Pickup Point Create' ,
            'shop_update'           =>  'Pickup Point Update',
            'shop_delete'           =>  'Pickup Point Delete',
            'payment_read'          =>  'Payment Read' ,
            'payment_create'        =>  'Payment Create',
            'payment_update'        =>  'Payment Update',
            'payment_delete'        =>  'Payment Delete' ,
            'reject'                =>  'Reject',
            'process'               =>   'Process',
            'status_change'         =>  'Status Change',
            'status_update'         =>  'Status Update',
            'permission_update'     =>  'Permission Update',
            'view'                  =>  'View',
            'parcel_status_reports' =>  'Parcel Status',
            'parcel_wise_profit'    =>  'Parcel Wise Profit',
            'parcel_total_summery'  =>  'Parcel total summery',
            'salary_reports'        =>   'Salary reports',
            'merchant_hub_deliveryman'=>  'Merchant/Branch/Deliveryman',
            'assets'                => 'Assets',
            'database_backup'       => 'Database Backup',
            'hub_payments_request'  =>  'Branch payments request',
            'hub_payments'          =>  'Branch payments',

            'total_Parcel'          => 'Total Parcel',
            'total_user'            => 'Total User',
            'total_merchant'        => 'Total Merchant',
            'total_delivery_man'    => 'Total Delivery Man',
            'total_hubs'            => 'Total Branch',
            'total_accounts'        =>  'Total Accounts',
            'total_parcels_pending' =>  'Total Parcels Pending',
            'total_pickup_assigned'         =>  'Total Pickup Assigned',
            'total_received_warehouse'      =>  'Total Received Warehouse',
            'total_deliveryman_assigned'    =>  'Total Deliveryman Assigned',
            'total_partial_deliverd'        =>  'Total Partial Delivered',
            'total_parcels_deliverd'        =>  'Total Parcels Deliverd',

            'recent_accounts'               =>  'Recent Account',
            'recent_salary'                 =>  'Recent Salary',
            'recent_hub'                    =>  'Recent Branch',
            'all_statements'                =>  'All Statements',
            'income_expense_charts'         =>  'Income Expense Chart',
            'merchant_revenue_charts'       =>  'Merchant Revenue Chart',
            'deliveryman_revenue_charts'    =>  'Deliveryman Revenue Chart',
            'courier_revenue_charts'        =>   'Courier Revenue Chart',
            'recent_parcels'                =>   'Recent Parcels',
            'bank_transaction'              =>   'Bank Transaction',
            'calendar'                      =>   'Calendar',
            'regular'                       =>   'Regular',
            'express'                       =>   'Express',
            'pickup_request'                =>   'Pickup Request',
            'subscribe'                     =>   'Subscribe',
            'invoice'                       =>   'Invoice',
            'status_update'                 =>   'Status Update',
            'social_login_settings'         =>   'Social Login Settings',
            'payout'                        =>   'Payout',
            'payout_setup_settings'         =>   'Payout Setup Settings',
            'hub_view'                      =>   'Branch View',
            'invoice_generate'              =>  'Invoice Generate',
            'paid_invoice_read'             =>   'Paid Invoice Read',
            'online_payment'                =>   'Online Payment',
            'currency'                      => 'Currency',

            //front web
            'social_link'                   => 'Social Link',
            'services'                      => 'Services',  
            'why_courier'              => 'Why Courier',
            'faq'                      => 'FAQ',
            'partner'                  => 'Partner', 
            'blogs'                    => 'Blogs',
            'pages'                    => 'Pages', 
            'sections'                  => 'Sections',
            'banner'                   => 'Banner',
            'wallet_request'           => 'Wallet Request',

    ];
