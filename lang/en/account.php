<?php

use App\Enums\AccountHeads;

return array (
  'title'                    => 'Accounts',
  'create_account'           => 'Create Account',
  'edit_account'             => 'Edit Account',
  'added_msg_'               => 'Account successfully added.',
  'update_msg'               => 'Account successfully update.',
  'delete_msg'               => 'Account successfully deleted.',
  'deliveryman_delete_msg'   => 'Cash Received from delivery man  successfully deleted.',
  'deliveryman_added_msg'    => 'Cash Received from delivery man  successfully created.',
  'deliveryman_update_msg'   => 'Cash Received from delivery man  successfully updated.',
  'error_msg'                => 'Something went wrong.',
  'income'                   => 'Income',
  'income_create'            => 'Income Create',
  'income_edit'              => 'Income Edit',
  'from'                     => 'From',
  'parcel'                   => 'Parcel',
  'to_account'               => 'To Account',
  'amount'                   => 'Amount',
  'receipt'                  => 'Receipt',
  'note'                     => 'Note',
  'date'                     => 'Date',
  AccountHeads::INCOME       => 'Income',
  AccountHeads::EXPENSE      => 'Expense',
  'opening_balance'          => 'Account Openning balance',
  'opening_balance_update'          => 'Account Openning balance update.',
  'opening_balance_reverse'  => 'Account Opening balance reverse',
  'added_msg'                => 'Cash received from delivery man successfully.',
  'added_msg'                => 'Account successfully added.',
  'not_enough_balance'       => 'Not Enough Balance.',
  'subscribe'                => 'Subscribe'
);
