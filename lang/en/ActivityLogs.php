<?php
return [
    'merchant.business_name'=>'Merchant',
    'payment_method'        => 'Payment Method',
    'bank_name'             =>  'Bank Name',
    'holder_name'           =>  'Holder Name',
    'account_no'            =>  'Account No.',
    'branch_name'           =>  'Branch Name',
    'routing_no'            =>  'Routing No.',
    'mobile_company'        =>  'Mobile Company',
    'mobile_no'             =>  'Mobile No.',
    'account_type'          =>  'Account Type',
    'user.name'             =>  'User',
    'month'                 =>  'Month',
    'amount'                => 'Amount',
    'status'                =>  'Status',
    'due'                   => 'Due',
    'advance'               => 'Advance',
    'note'                  => 'Note',
    'account_holder_name'   =>  'Holder Name',
    'gateway'               => 'Gateway',
    'name'                 =>  'Name',
    'assetcategorys.title' => 'Asset Category',
    'hubs.name'            => 'Hub',
    'supplyer_name'        => 'Supplyer',
    'quantity'             => 'Quantity',
    'warranty'             => 'Warranty',
    'invoice_no'           => 'Invoice_no',
    'description'          => 'Description',
    'title'                => 'Title',
    'position'             => 'Position',
    'category.name'        => 'Category',
    'weight'               => 'Weight',
    'same_day'             => 'Same Day',
    'next_day'             => 'Next Day',
    'sub_city'             =>  'Sub City',
    'outside_city'         =>  'Outside City',
    'current_balance'      => 'Current Balance',
    'deliveryman.user.name'=> 'Delivery man',
    'parcel.tracking_id'   => 'Parcel',
    'account.account_no'   => 'Account No.',
    'date'                 => 'Date',
    'receipt'              => 'receipt',
    'createdby.name'       => 'Created By',
    'phone'                => 'Phone',
    'tracking_id'          => 'Tracking_id',
    'details'              => 'Details',
    'fromAccount.account_no'=> 'From Account',
    'toAccount.account_no'  => 'To Account',
    'address'               =>  'Address',
    'hub.name'              => 'Hub',
    'transaction_id'        => 'Transaction Id',
    'fromPayment.account_no'=> 'From Account',
    'frompayment.account_no'=> 'From Account',
    'business_name'         => 'Business Name',
    'deliveryCharge.category.title'=> 'Delivery Category',
    'price'                  => 'Price',
    'pickup_address'         =>  'Pickup Address',
    'pickup_phone'           =>  'Pickup Phone',
    'customer_name'          =>  'Customer Name',
    'customer_phone'         =>  'Customer Phone',
    'customer_address'       =>  'Customer Address',
    'invoice_no'             =>  'Invoice No',
    'cash_collection'        =>  'Cash Collection',
    'selling_price'          =>  'Selling Price',
    'delivery_charge'        =>   'Delivery Charge',
    'total_delivery_amount'  =>   'Total Delivery Amount',
    'current_payable'        =>  'Current Payable',
    'merchantAccount.holder_name'=> 'Holder Name',
    'permissions'            => 'Permissions',
    'account'                =>  'Account',
    'sms_send_status'        =>  'SMS Send Status',
    'api_key'                =>  'API Key',
    'secret_key'             =>  'Secret Key',
    'department.title'       =>  'Department',
    'service'                =>  'Service',
    'priority'               =>  'Priority',
    'subject'                =>  'Subject',
    'original'               =>  'Original',
    'user.hub.name'          =>  'Hub',
    'slug'                   =>  'Slug',
    'key'                    =>  'Key',
    'value'                  =>  'Value',
    'contact_no'             =>  'Contact No.',
    'email'                  =>   'Email',
    'password'               =>   'Password'

];
