<?php

return array (
  'dashboard'           => 'Dashboard',
  'merchant_dashboard'  => 'Merchant Dashboard',
  'title'               => 'Merchant',
  'delivery_charge'     => 'Delivery Charge',
  'shop'                => 'Pickup Points',
  'company_information' => 'Company info',
  'opening_balance'     => 'Opening balance',
  'vat'                 => 'Vat',
  'cod_charges'         => 'Cod charges',
  'weight'              => 'Weight',
  'category'            => 'Category',
  'same_day'            => 'Same day',
  'next_day'            => 'Next day',
  'sub_city'            => 'Sub city',
  'inside_city'         => 'Inside city',
  'outside_city'        => 'Outside city',
  'create_delivery_charge'     => 'Create Delivery Charge',
  'edit_delivery_charge'     => 'Edit Delivery Charge',
  'create_merchant'     => 'Create Merchant',
  'edit_merchant'       => 'Edit Merchant',
  'delivery_charge_added_msg'           => 'Merchant Delivery Charge successfully added.',
  'added_msg'           => 'Merchant successfully added.',
  'update_msg'          => 'Merchant successfully update.',
  'delivery_charge_update_msg'          => 'Merchant  Delivery Charge successfully update.',
  'delete_msg'          => 'Merchant successfully deleted.',
  'delivery_charge_delete_msg'          => 'Merchant  Delivery Charge successfully deleted.',
  'error_msg'           => 'Something went wrong.',
  'delivery_charge_error_msg'           => 'Something went wrong.',
  'account_info'                        => 'Account Information',

  //merchant payment
  'payment_added_msg'           => 'Merchant Payment successfully added.',
  'payment_error_msg'           => 'Something went wrong.',
  'payment_account_delete_msg'  => 'Merchant  Payment account successfully deleted.',
  'payment_update_msg'          => 'Merchant  payment account successfully update.',

  'payment_account'     => 'Payment Account',
  'payment_info'       => 'Payment Information',
  'bank'               => 'Bank',
  'bank_name'               => 'Bank Name',
  'mobile'             => 'Mobile',
  'payment_method'     => 'Payment Method',
  'select_payment_method'     => 'Select Payment Method',
  'select_bank'           => 'Select Bank',

  'holder_name'           => 'Holder Name',
  'branch_name'           => 'Branch Name',
  'account_no'                  =>  'Account No',
  'routing_no'                  =>  'Routing No',

  // banks
  "ab_bank_ltd"                 =>  "AB Bank Ltd.",
  "agrani_bank_ltd"             => "Agrani Bank Ltd.",
  "al_afarah_islami_bank_ltd"   =>  "Al-Arafah Islami Bank Ltd.",
  "bank_asia_ltd"               =>  "Bank Asia Ltd.",
  "brac_bank_ltd"               =>  "Brac Bank Ltd.",
  "city_bank_ltd"               => "City Bank Ltd.",
  "dbbl_agent_banking"          =>  "DBBL Agent Banking",
  "dhaka_bank_limited"          =>  "Dhaka Bank Ltd.",
  "dutch_bangla_bank_ltd"       =>  "Dutch-Bangla Bank Ltd.",
  "eastern_bank_ltd"            =>  "Eastern Bank Ltd.",
  "exim_bank_bangladesh_ltd"    => "Exim Bank Bangladesh Ltd.",
  "ific_bank_ltd"               =>  "IFIC Bank Ltd.",
  "islami_bank_bangladesh_ltd"  =>  "Islami Bank Bangladesh Ltd.",
  "jamuna_bank_ltd"             =>  "Jamuna Bank Ltd.",
  "mercantile_bank_ltd"         =>  "Mercantile Bank Ltd.",
  "mutual_trust_bank_ltd"       =>  "Mutual Trust Bank Ltd.",
  "national_bank_ltd"           =>  "National Bank Ltd.",
  "nrb_bank_ltd"                =>  "NRB Bank Ltd.",
  "nrb_commercial_bank_ltd"     =>  "NRB Commercial Bank Ltd.",
  "one_bank_ltd"                =>  "One Bank Ltd.",
  "prime_bank_ltd"              =>  "Prime Bank Ltd.",
  "southeast_bank_ltd"          =>  "Southeast Bank Ltd.",
  "standard_bank_ltd"           =>  "Standard Bank Ltd.",
  "standard_chartered_bank"     =>  "Standard Chartered Bank",
  "the_premiere_bank_ltd"       =>  "The Premier Bank Ltd.",
  "trust_bank_ltd"              =>  "Trust Bank Ltd.",
  "united_commercial_bank_ltd"  =>  "United Commercial Bank Ltd.",
  "uttara_bank_ltd"             =>  "Uttara Bank Ltd.",


  'select_mobile_company'      =>   'Select Company',
  'mobile_company'             =>   'Mobile Company',
  'bkash'                      =>   'Bkash',
  'nogod'                      =>   'Nagad',
  'rocket'                     =>   'Rocket',

  'mobile_no'                  =>  'Mobile No.',
  'account_type'               =>  'Account Type',

  'personal'                 =>   'Personal',
  'merchant'                 =>   'Merchant',

  //merchant panel accounts
  'create_account'           =>   'Create Payment Account',
  'edit_account'             =>   'Edit Payment Account',
  'account'                  =>    'Account',
  'payment_accounts'         =>'Payment Information',
  'create'                   => 'Create',
  'edit'                     => 'Edit',
  'business_name'            => 'Business Name',
  'invoice'                  => 'Invoice No',
  'track_id'                  => 'Tracking ID',
  'amount'                   =>  'Amount',
  'payble_amount'            =>  'Payable Amount',
  'payment_request'          =>  'Payment Request',
  'invalid_otp'              =>  'Invalid OTP',
  'cash'                     => 'Cash',
  'wallet' => 'Wallet',
  

);
